# ADB Control Panel - Setup & Usage Guide

## 🚀 Quick Start

This is a web-based Android Debug Bridge (ADB) control panel that allows you to manage Android devices through your browser.

### Prerequisites

1. **Node.js** (version 14 or higher)
   - Download from: https://nodejs.org/
   - Verify installation: `node --version` and `npm --version`

2. **Android Device with USB Debugging Enabled**
   - Go to Settings → About Phone → Tap "Build Number" 7 times
   - Go to Settings → Developer Options → Enable "USB Debugging"
   - Connect device via USB and allow debugging when prompted

## 📦 Installation

1. **Extract the ZIP file** to your desired location
2. **Open terminal/command prompt** in the extracted folder
3. **Install dependencies:**
   ```bash
   npm install
   ```

## ▶️ Running the Application

1. **Start the server:**
   ```bash
   node server.js
   ```

2. **Open your browser** and go to:
   ```
   http://localhost:3000
   ```

3. **Connect your Android device** via USB and click "List Devices"

## 🎯 Features

### Device Management
- **List Devices**: See all connected ADB devices
- **Auto-select**: Automatically selects device if only one is connected
- **Multi-device support**: Work with multiple devices simultaneously

### Screen Mirroring
- **Real-time mirroring**: Live view of your device screen
- **Click to start**: Click on a device name to auto-select and start mirroring
- **Optimized performance**: 2 FPS to reduce device load

### Device Controls
- **Hardware buttons**: Home, Back, Recent Apps
- **Gestures**: Swipe Up, Left, Right
- **Device management**: Reboot single device or all devices
- **Unlock**: Attempt to unlock device with swipe gesture

### APK Installation
- **Single device**: Install APK on selected device
- **All devices**: Install APK on all connected devices
- **Drag & drop**: Easy file selection

## 🛠️ Troubleshooting

### Common Issues

**"No devices found"**
- Ensure USB debugging is enabled
- Check USB cable connection
- Try different USB port
- Restart ADB: Open terminal in `platform-tools` folder and run:
  ```bash
  ./adb kill-server
  ./adb start-server
  ```

**"Port 3000 already in use"**
- Find and kill the process:
  ```bash
  # Windows
  netstat -ano | findstr :3000
  taskkill /PID [PID_NUMBER] /F
  
  # Mac/Linux
  lsof -ti:3000 | xargs kill
  ```

**"Device unauthorized"**
- Check your device screen for USB debugging authorization prompt
- Select "Always allow from this computer"

**Screen mirroring not working**
- Ensure device is unlocked
- Try restarting the mirroring
- Check if device supports screen capture

## 📁 Project Structure

```
adb_control_panel/
├── server.js           # Main server file
├── index.html          # Web interface
├── package.json        # Dependencies
├── platform-tools/     # ADB executable and tools
│   ├── adb.exe         # ADB executable (Windows)
│   └── ...             # Other ADB tools
└── README.md           # Original documentation
```

## 🔧 Technical Details

- **Backend**: Node.js with Express
- **WebSocket**: Real-time screen mirroring
- **File Upload**: Multer for APK handling
- **Frontend**: Vanilla HTML/CSS/JavaScript
- **ADB Integration**: Direct command execution

## 📱 Supported ADB Commands

- `adb devices` - List connected devices
- `adb reboot` - Reboot device
- `adb shell input keyevent` - Send key events
- `adb shell input swipe` - Send swipe gestures
- `adb exec-out screencap` - Capture screen
- `adb install` - Install APK files

## 🚨 Important Notes

- Keep your device unlocked for best experience
- Some secure lock screens may prevent certain gestures
- Screen mirroring works best with devices that have sufficient resources
- Always safely disconnect devices when done

## 📞 Support

If you encounter issues:
1. Check the browser console for errors (F12)
2. Check the terminal output for server errors
3. Ensure your device drivers are properly installed
4. Try restarting both the server and your device

---

**Enjoy controlling your Android devices from your browser! 🎉**
