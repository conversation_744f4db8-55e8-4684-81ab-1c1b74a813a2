# ADB Control Panel

This project provides a web-based interface to control your Android devices using ADB (Android Debug Bridge).

## Prerequisites

Before you begin, ensure you have the following installed:

1.  **Node.js and npm:**
    *   Download and install Node.js from [https://nodejs.org/](https://nodejs.org/). npm (Node Package Manager) is included with Node.js.
2.  **Android Debug Bridge (ADB):**
    *   The `platform-tools` directory containing `adb.exe` is already included in this project. You do not need to install ADB separately if you use the provided `adb.exe`.
    *   **On your Android Device:**
        *   Enable **Developer Options**: Go to `Settings` > `About phone` (or `About device`) and tap `Build number` seven times.
        *   Enable **USB debugging**: In `Settings` > `Developer options`, toggle on `USB debugging`.
        *   When you connect your device to your computer for the first time with USB debugging enabled, you might see a pop-up asking to "Allow USB debugging?". Always allow from this computer.

## Installation

1.  **Clone or Download the Project:**
    *   If you're using Git, clone this repository:
        ```bash
        git clone <repository_url>
        cd adb_project
        ```
    *   Otherwise, download the project as a ZIP file and extract it to your desired location. Navigate into the extracted `adb_project` directory.

2.  **Install Dependencies:**
    *   Open your terminal or command prompt.
    *   Navigate to the project's root directory (where `package.json` is located):
        ```bash
        cd F:\gemini_project\adb_project
        ```
    *   Install the Node.js dependencies:
        ```bash
        npm install
        ```

## Running the Server

1.  **Start the Server:**
    *   In the project's root directory, run the following command:
        ```bash
        node server.js
        ```
    *   You should see output similar to: `Server listening at http://localhost:3000`

2.  **Access the Web Interface:**
    *   Open your web browser and go to: `http://localhost:3000`

## Usage

*   **List Devices:** Click the "List Devices" button to see connected ADB devices.
*   **Select and Mirror:** Click on a listed device name to automatically select it and start screen mirroring.
*   **Device Actions:** Once screen mirroring starts, a "Device Actions" section will appear with buttons for rebooting, sending key events (Home, Back, Recents), and swipe gestures.
*   **Install APK:** Upload an APK file and install it on the selected device or all connected devices.

## Troubleshooting

*   **`EADDRINUSE: address already in use :::3000`**:
    This means the server is already running or a previous instance didn't shut down cleanly.
    1.  Find the process using port 3000:
        ```bash
        netstat -ano | findstr :3000
        ```
        Look for the PID (Process ID) in the last column.
    2.  Terminate the process (replace `YOUR_PID_HERE` with the actual PID):
        ```bash
        taskkill /PID YOUR_PID_HERE /F
        ```
    3.  Then, try `node server.js` again.

*   **`error: device 'YOUR_DEVICE_ID' not found`**:
    This means ADB cannot communicate with your device.
    1.  **Check USB Connection:** Ensure your device is properly connected via USB.
    2.  **USB Debugging Authorization:** Look at your device screen for an "Allow USB debugging?" prompt and accept it.
    3.  **Restart ADB Server:**
        *   Open a new terminal/command prompt.
        *   Navigate to `F:\gemini_project\adb_project\platform-tools`
        *   Run: `.\adb kill-server`
        *   Run: `.\adb start-server`
        *   Verify device connection: `.\adb devices`
    4.  **ADB Drivers:** Ensure you have the correct ADB drivers installed for your device on your computer. Often, installing your phone manufacturer's USB drivers or the Google USB Driver (via Android Studio's SDK Manager) can resolve this.

*   **Device Freezing during Screen Mirroring:**
    The screen capture frequency has been reduced to mitigate this. If it still occurs, your device might be struggling with the load. Ensure your device has sufficient resources.

*   **Swipe/Unlock Gestures Not Working:**
    Modern Android devices with pattern/PIN/fingerprint locks often prevent `input keyevent 82` (menu button) from unlocking. The current setup uses a swipe gesture, which is more effective for simple swipe locks. For devices with secure locks, you will need to manually unlock them or disable the secure lock screen for the duration of use.

## Project Structure

*   `index.html`: The main web interface.
*   `server.js`: The Node.js backend server that handles ADB commands and WebSocket communication for screen mirroring.
*   `package.json`: Defines project metadata and Node.js dependencies.
*   `node_modules/`: Directory containing installed Node.js packages (created after `npm install`).
*   `platform-tools/`: Contains the `adb.exe` executable and related DLLs.
*   `uploads/`: Temporary directory for uploaded APK files.
*   `adb_project_backup.tar.gz`: A backup of the project created by Gemini.
*   `README.md`: This file.
