<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADB Control Panel</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f4f4f4; }
        .container { background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); max-width: 800px; margin: auto; }
        h1, h2 { color: #333; }
        button { background-color: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 5px; cursor: pointer; margin: 5px 0; }
        button:hover { background-color: #0056b3; }
        input[type="text"] { width: calc(100% - 22px); padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        .device-list, .output-area { background-color: #e9ecef; padding: 10px; border-radius: 5px; margin-top: 10px; min-height: 50px; overflow-y: auto; }
        .screenshot-area img, .screen-mirror-area img { max-width: 100%; height: auto; border: 1px solid #ddd; margin-top: 10px; }
        .screen-mirror-area { position: relative; display: inline-block; }
        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #eee; border-radius: 8px; background-color: #fdfdfd; }
        .section h3 { margin-top: 0; margin-bottom: 10px; color: #555; font-size: 16px; }
        .section div { margin-bottom: 10px; }
        #touchIndicator { background-color: rgba(255, 0, 0, 0.3); border: 2px solid #ff0000; animation: pulse 0.3s ease-out; }
        @keyframes pulse { 0% { transform: scale(0.8); opacity: 1; } 100% { transform: scale(1.2); opacity: 0; } }

        /* Device installation status styles */
        .device-item { padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid #ddd; }
        .device-newly-discovered { border-left-color: #ffc107; background-color: #fff3cd; }
        .device-installed { border-left-color: #28a745; background-color: #d4edda; }
        .device-offline { border-left-color: #6c757d; background-color: #f8f9fa; }
        .device-status { font-size: 12px; color: #666; margin-top: 5px; }
        .device-alias { font-weight: bold; color: #007bff; }
        .installation-table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .installation-table th, .installation-table td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        .installation-table th { background-color: #f8f9fa; }
        .status-badge { padding: 2px 6px; border-radius: 3px; font-size: 11px; font-weight: bold; }
        .status-newly-discovered { background-color: #ffc107; color: #212529; }
        .status-installed { background-color: #28a745; color: white; }
        .status-offline { background-color: #6c757d; color: white; }

        /* Dynamic IP management styles */
        .connectivity-status { margin-left: 8px; font-size: 14px; }
        .device-actions { display: flex; gap: 5px; flex-wrap: wrap; }
        .device-actions button { padding: 4px 8px; font-size: 11px; border-radius: 3px; }
        .ip-info { font-family: monospace; background-color: #f8f9fa; padding: 4px 8px; border-radius: 3px; display: inline-block; margin: 2px 0; }
        .network-tools { margin-top: 15px; padding: 10px; background-color: #e9ecef; border-radius: 5px; }
        .network-tools button { margin-right: 10px; margin-bottom: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ADB Control Panel</h1>

        <div class="section">
            <h2>Device Management</h2>
            <label for="deviceId">Select Device:</label>
            <select id="deviceId">
                <option value="">-- Select a device --</option>
            </select>
            <button onclick="listDevices()">List Devices</button>
            <button onclick="showInstalledDevices()">Show Installation History</button>
            <button onclick="refreshAllIPs()">🔄 Refresh All IPs</button>
            <button onclick="testAllConnectivity()">🧪 Test Connectivity</button>
            <div class="device-list" id="deviceList">No devices listed.</div>

            <div id="installationHistory" style="display: none; margin-top: 15px;">
                <h3>📱 Device Installation History</h3>
                <div id="installedDevicesList"></div>
            </div>
        </div>

        <div class="section">
            <h2>Screen Mirroring</h2>
            <button onclick="startScreenMirroring()">Start Mirroring</button>
            <button onclick="stopScreenMirroring()">Stop Mirroring</button>
            <div class="screen-mirror-area">
                <img id="mirrorImage" src="" alt="Device Screen Mirror" style="display: none; cursor: crosshair; user-select: none;">
                <div id="touchIndicator" style="position: absolute; width: 20px; height: 20px; border: 2px solid red; border-radius: 50%; display: none; pointer-events: none; z-index: 1000;"></div>
            </div>
            <div class="section" id="deviceActionsSection" style="display: none;">
                <h2>Device Actions</h2>
                <div style="margin-bottom: 15px;">
                    <h3>Power Controls</h3>
                    <button onclick="powerButton('sleep')">Sleep/Wake Device</button>
                    <button onclick="powerButton('wake')">Power Button</button>
                </div>

                <div style="margin-bottom: 15px;">
                    <h3>Navigation</h3>
                    <button onclick="sendKeyEvent('HOME')">Home Button</button>
                    <button onclick="sendKeyEvent('BACK')">Back Button</button>
                    <button onclick="sendKeyEvent('RECENTS')">Recents Button</button>
                </div>

                <div style="margin-bottom: 15px;">
                    <h3>Gestures</h3>
                    <button onclick="swipeUp()">Swipe Up</button>
                    <button onclick="swipeLeft()">Swipe Left</button>
                    <button onclick="swipeRight()">Swipe Right</button>
                </div>

                <div style="margin-bottom: 15px;">
                    <h3>Unlock Device</h3>
                    <button onclick="checkLockStatus()">Check Lock Status</button>
                    <button onclick="advancedUnlock('wake_sequence')">🔓 Wake & Unlock</button>
                    <button onclick="advancedUnlock('wake_and_swipe')">⚡ Wake Device</button>
                    <button onclick="advancedUnlock('swipe_up_small')">⬆️ Swipe Up</button>
                    <button onclick="advancedUnlock('multiple_swipes')">🔄 Multiple Swipes</button>
                    <button onclick="advancedUnlock('menu_key')">🔑 Menu Key</button>
                    <div id="lockStatus" style="margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 4px; display: none;"></div>
                </div>

                <div style="margin-bottom: 15px;">
                    <h3>Multi-touch</h3>
                    <button onclick="pinchZoom(1.5)">Pinch Zoom Out</button>
                    <button onclick="pinchZoom(0.7)">Pinch Zoom In</button>
                    <button onclick="twoFingerScroll('up')">Two-Finger Scroll Up</button>
                    <button onclick="twoFingerScroll('down')">Two-Finger Scroll Down</button>
                </div>

                <div style="margin-bottom: 15px;">
                    <h3>Device Management</h3>
                    <button onclick="rebootDevice()">Reboot Selected Device</button>
                    <button onclick="rebootAllDevices()">Reboot All Devices</button>
                </div>

                <div style="margin-bottom: 15px;">
                    <h3>Touch Mode</h3>
                    <label>
                        <input type="checkbox" id="touchModeEnabled" checked>
                        Enable Click-to-Tap (click on screen to interact)
                    </label>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Install APK</h2>
            <input type="file" id="apkFile" accept=".apk">
            <button onclick="installApk()">Install APK on Selected Device</button>
            <button onclick="installApkAllDevices()">Install APK on All Devices</button>
        </div>

        <div class="section">
            <h2>Output</h2>
            <div class="output-area" id="outputArea"></div>
        </div>
    </div>

    <script>
        const deviceIdInput = document.getElementById('deviceId');
        const deviceListDiv = document.getElementById('deviceList');
        const outputAreaDiv = document.getElementById('outputArea');
        const mirrorImage = document.getElementById('mirrorImage');
        const deviceActionsSection = document.getElementById('deviceActionsSection');
        const touchIndicator = document.getElementById('touchIndicator');

        let screenMirroringSocket = null;
        let deviceScreenWidth = 1080; // Default values
        let deviceScreenHeight = 1920;

        function logOutput(message) {
            outputAreaDiv.innerHTML += `<p>${message}</p>`;
            outputAreaDiv.scrollTop = outputAreaDiv.scrollHeight; // Auto-scroll
        }

        async function listDevices() {
            logOutput('Listing devices...');
            try {
                const response = await fetch('/api/list_devices');
                const data = await response.json();
                if (data.success) {
                    // Display devices with installation status
                    if (data.devices.length > 0) {
                        deviceListDiv.innerHTML = data.devices.map(device => {
                            const statusClass = `device-${device.installationStatus.replace('_', '-')}`;
                            const statusBadge = `<span class="status-badge status-${device.installationStatus.replace('_', '-')}">${device.installationStatus.replace('_', ' ').toUpperCase()}</span>`;
                            const aliasDisplay = device.aliases.length > 0 ? `<span class="device-alias">${device.aliases[0]}</span> - ` : '';
                            const connectivityIcon = device.connectivity.isOnline ? '🟢' : '🔴';
                            const ipDisplay = device.currentIP !== 'Unknown' ? device.currentIP : 'No IP';
                            const macDisplay = device.macAddress !== 'Unknown' ? device.macAddress.substring(0, 17) : 'Unknown';
                            const ipHistoryCount = device.ipHistory ? device.ipHistory.length : 0;

                            return `
                                <div class="device-item ${statusClass}">
                                    <div>
                                        <a href="#" onclick="selectAndMirrorDevice('${device.id}')">
                                            ${aliasDisplay}${device.id} (${device.status})
                                        </a>
                                        ${statusBadge}
                                        <span class="connectivity-status">${connectivityIcon}</span>
                                    </div>
                                    <div class="device-status">
                                        📍 Current IP: <strong>${ipDisplay}</strong> |
                                        🔗 MAC: ${macDisplay}<br>
                                        📅 First seen: ${new Date(device.firstSeen).toLocaleString()}<br>
                                        🔄 Connections: ${device.totalConnections} |
                                        ⏱️ Last seen: ${new Date(device.lastSeen).toLocaleString()}
                                        ${ipHistoryCount > 0 ? `| 📊 IP History: ${ipHistoryCount} changes` : ''}
                                    </div>
                                    <div class="device-actions" style="margin-top: 8px;">
                                        <button onclick="setDeviceAlias('${device.id}')" style="font-size: 11px;">Set Alias</button>
                                        <button onclick="refreshDeviceIP('${device.id}')" style="font-size: 11px;">🔄 Refresh IP</button>
                                        <button onclick="testDeviceConnectivity('${device.id}')" style="font-size: 11px;">🧪 Test</button>
                                        <button onclick="showIPHistory('${device.id}')" style="font-size: 11px;">📊 IP History</button>
                                    </div>
                                </div>
                            `;
                        }).join('');

                        // Populate the dropdown with device IDs
                        const deviceSelect = document.getElementById('deviceId');
                        deviceSelect.innerHTML = '<option value="">-- Select a device --</option>';
                        data.devices.forEach(device => {
                            const option = document.createElement('option');
                            option.value = device.id;
                            const displayName = device.aliases.length > 0 ? `${device.aliases[0]} (${device.id})` : device.displayName;
                            option.textContent = displayName;
                            deviceSelect.appendChild(option);
                        });

                        // Automatically select the first device if only one is present
                        if (data.devices.length === 1) {
                            deviceSelect.value = data.devices[0].id;
                        }
                    } else {
                        deviceListDiv.innerHTML = 'No devices found.';
                    }
                    logOutput('Devices listed successfully.');
                } else {
                    logOutput(`Error listing devices: ${data.message}`);
                }
            } catch (error) {
                logOutput(`Network error listing devices: ${error.message}`);
            }
        }

        async function rebootDevice() {
            const deviceId = deviceIdInput.value;
            if (!deviceId) { logOutput('Please enter a Device ID.'); return; }
            logOutput(`Rebooting ${deviceId}...`);
            try {
                const response = await fetch('/api/reboot_device', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deviceId })
                });
                const data = await response.json();
                logOutput(data.message);
            } catch (error) {
                logOutput(`Network error rebooting device: ${error.message}`);
            }
        }

        async function rebootAllDevices() {
            logOutput('Rebooting all connected devices...');
            const devices = Array.from(document.getElementById('deviceId').options)
                               .filter(option => option.value !== '')
                               .map(option => option.value);

            if (devices.length === 0) {
                logOutput('No devices found to reboot.');
                return;
            }

            for (const deviceId of devices) {
                try {
                    const response = await fetch('/api/reboot_device', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ deviceId })
                    });
                    const data = await response.json();
                    logOutput(data.message);
                } catch (error) {
                    logOutput(`Network error rebooting ${deviceId}: ${error.message}`);
                }
            }
        }

        async function sendKeyEvent(keyEvent) {
            const deviceId = deviceIdInput.value;
            if (!deviceId) { logOutput('Please enter a Device ID.'); return; }
            logOutput(`Sending ${keyEvent} key event to ${deviceId}...`);
            try {
                const response = await fetch('/api/send_keyevent', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deviceId, keyEvent })
                });
                const data = await response.json();
                logOutput(data.message);
            }
            catch (error) {
                logOutput(`Network error sending key event: ${error.message}`);
            }
        }

        

        async function sendText() {
            const deviceId = deviceIdInput.value;
            const text = textInput.value;
            if (!deviceId || !text) { logOutput('Please enter a Device ID and text.'); return; }
            logOutput(`Sending text "${text}" to ${deviceId}...`);
            try {
                const response = await fetch('/api/send_text', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deviceId, text })
                });
                const data = await response.json();
                logOutput(data.message);
            } catch (error) {
                logOutput(`Network error sending text: ${error.message}`);
            }
        }

        async function swipeUp() {
            const deviceId = deviceIdInput.value;
            if (!deviceId) { logOutput('Please enter a Device ID.'); return; }
            logOutput(`Sending swipe up gesture to ${deviceId}...`);
            try {
                const response = await fetch('/api/swipe_up', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deviceId })
                });
                const data = await response.json();
                logOutput(data.message);
            } catch (error) {
                logOutput(`Network error sending swipe up: ${error.message}`);
            }
        }

        async function unlockDevice() {
            const deviceId = deviceIdInput.value;
            if (!deviceId) { logOutput('Please enter a Device ID.'); return; }
            logOutput(`Attempting to unlock ${deviceId} with a swipe up gesture...`);
            // Call the existing swipeUp function
            swipeUp();
        }

        async function swipeLeft() {
            const deviceId = deviceIdInput.value;
            if (!deviceId) { logOutput('Please enter a Device ID.'); return; }
            logOutput(`Sending swipe left gesture to ${deviceId}...`);
            try {
                const response = await fetch('/api/swipe_left', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deviceId })
                });
                const data = await response.json();
                logOutput(data.message);
            } catch (error) {
                logOutput(`Network error sending swipe left: ${error.message}`);
            }
        }

        async function swipeRight() {
            const deviceId = deviceIdInput.value;
            if (!deviceId) { logOutput('Please enter a Device ID.'); return; }
            logOutput(`Sending swipe right gesture to ${deviceId}...`);
            try {
                const response = await fetch('/api/swipe_right', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deviceId })
                });
                const data = await response.json();
                logOutput(data.message);
            } catch (error) {
                logOutput(`Network error sending swipe right: ${error.message}`);
            }
        }

        function startScreenMirroring() {
            const deviceId = deviceIdInput.value;
            if (!deviceId) { logOutput('Please enter a Device ID.'); return; }

            if (screenMirroringSocket) {
                logOutput('Screen mirroring already active.');
                return;
            }

            // Get device screen resolution first
            getDeviceResolution(deviceId);

            logOutput(`Starting screen mirroring for ${deviceId}...`);
            mirrorImage.style.display = 'block';
            deviceActionsSection.style.display = 'block'; // Show device actions

            // Add click event listener for touch simulation
            setupTouchEvents();

            screenMirroringSocket = new WebSocket(`ws://localhost:3000?deviceId=${deviceId}`);
            screenMirroringSocket.binaryType = 'arraybuffer';

            screenMirroringSocket.onopen = () => {
                logOutput('Screen mirroring connected.');
            };

            screenMirroringSocket.onmessage = (event) => {
                console.log('Received new image data.'); // Log to confirm data reception
                const blob = new Blob([event.data], { type: 'image/png' });
                // Add cache-busting parameter
                const imageUrl = URL.createObjectURL(blob) + '#' + new Date().getTime();
                mirrorImage.src = imageUrl;
            };

            screenMirroringSocket.onclose = () => {
                logOutput('Screen mirroring disconnected.');
                screenMirroringSocket = null;
                mirrorImage.style.display = 'none';
                deviceActionsSection.style.display = 'none'; // Hide device actions
                removeTouchEvents();
            };

            screenMirroringSocket.onerror = (error) => {
                logOutput(`Screen mirroring error: ${error.message}`);
                console.error('WebSocket error:', error);
                deviceActionsSection.style.display = 'none'; // Hide device actions on error
                removeTouchEvents();
            };
        }

        function stopScreenMirroring() {
            if (screenMirroringSocket) {
                screenMirroringSocket.close();
                logOutput('Screen mirroring stopped.');
            } else {
                logOutput('Screen mirroring not active.');
            }
        }

        async function installApk() {
            const deviceId = deviceIdInput.value;
            const apkFile = document.getElementById('apkFile').files[0];

            if (!deviceId) {
                logOutput('Please select a Device ID.');
                return;
            }

            if (!apkFile) {
                logOutput('Please select an APK file to install.');
                return;
            }

            logOutput(`Installing ${apkFile.name} on ${deviceId}...`);

            const formData = new FormData();
            formData.append('deviceId', deviceId);
            formData.append('apkFile', apkFile);

            try {
                const response = await fetch('/api/install_apk', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                logOutput(data.message);
            } catch (error) {
                logOutput(`Network error installing APK: ${error.message}`);
            }
        }

        async function installApkAllDevices() {
            const apkFile = document.getElementById('apkFile').files[0];

            if (!apkFile) {
                logOutput('Please select an APK file to install.');
                return;
            }

            logOutput(`Installing ${apkFile.name} on all connected devices...`);

            const devices = Array.from(document.getElementById('deviceId').options)
                               .filter(option => option.value !== '')
                               .map(option => option.value);

            if (devices.length === 0) {
                logOutput('No devices found to install APK on.');
                return;
            }

            // Install on all devices using the new bulk install endpoint
            const formData = new FormData();
            formData.append('apkFile', apkFile);
            devices.forEach(deviceId => {
                formData.append('deviceIds', deviceId);
            });

            try {
                const response = await fetch('/api/install_apk_bulk', {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                logOutput(data.message);
                if (data.results) {
                    data.results.forEach(result => {
                        logOutput(result.message);
                    });
                }
            } catch (error) {
                logOutput(`Network error installing APK on all devices: ${error.message}`);
            }
        }

        // Automatically list devices on page load
        window.onload = listDevices;

        function selectAndMirrorDevice(deviceId) {
            deviceIdInput.value = deviceId;
            startScreenMirroring();
        }

        // Get device screen resolution
        async function getDeviceResolution(deviceId) {
            try {
                const response = await fetch(`/api/screen_resolution/${deviceId}`);
                const data = await response.json();
                if (data.success) {
                    deviceScreenWidth = data.width;
                    deviceScreenHeight = data.height;
                    logOutput(`Device resolution: ${data.resolution}`);
                } else {
                    logOutput('Could not get device resolution, using defaults');
                }
            } catch (error) {
                logOutput(`Error getting device resolution: ${error.message}`);
            }
        }

        // Setup touch event listeners
        function setupTouchEvents() {
            mirrorImage.addEventListener('click', handleImageClick);
            mirrorImage.addEventListener('wheel', handleWheelEvent, { passive: false });

            // Add touch events for mobile
            mirrorImage.addEventListener('touchstart', handleTouchStart, { passive: false });
            mirrorImage.addEventListener('touchmove', handleTouchMove, { passive: false });
            mirrorImage.addEventListener('touchend', handleTouchEnd, { passive: false });
        }

        // Remove touch event listeners
        function removeTouchEvents() {
            mirrorImage.removeEventListener('click', handleImageClick);
            mirrorImage.removeEventListener('wheel', handleWheelEvent);
            mirrorImage.removeEventListener('touchstart', handleTouchStart);
            mirrorImage.removeEventListener('touchmove', handleTouchMove);
            mirrorImage.removeEventListener('touchend', handleTouchEnd);
        }

        // Handle click on mirrored screen
        function handleImageClick(event) {
            const touchModeEnabled = document.getElementById('touchModeEnabled').checked;
            if (!touchModeEnabled) return;

            const rect = mirrorImage.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // Calculate device coordinates
            const deviceX = Math.round((x / rect.width) * deviceScreenWidth);
            const deviceY = Math.round((y / rect.height) * deviceScreenHeight);

            // Show touch indicator
            showTouchIndicator(x + rect.left, y + rect.top);

            // Send tap command
            sendTouchTap(deviceX, deviceY);
        }

        // Show visual feedback for touch
        function showTouchIndicator(x, y) {
            touchIndicator.style.left = (x - 10) + 'px';
            touchIndicator.style.top = (y - 10) + 'px';
            touchIndicator.style.display = 'block';

            setTimeout(() => {
                touchIndicator.style.display = 'none';
            }, 300);
        }

        // Send touch/tap command to device
        async function sendTouchTap(x, y) {
            const deviceId = deviceIdInput.value;
            if (!deviceId) return;

            try {
                const response = await fetch('/api/touch_tap', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deviceId, x, y })
                });
                const data = await response.json();
                if (data.success) {
                    logOutput(`Tap sent to (${x}, ${y})`);
                } else {
                    logOutput(`Error sending tap: ${data.message}`);
                }
            } catch (error) {
                logOutput(`Network error sending tap: ${error.message}`);
            }
        }

        // Handle wheel events for zoom simulation
        function handleWheelEvent(event) {
            event.preventDefault();
            const scale = event.deltaY > 0 ? 0.8 : 1.2;
            const rect = mirrorImage.getBoundingClientRect();
            const centerX = Math.round(((event.clientX - rect.left) / rect.width) * deviceScreenWidth);
            const centerY = Math.round(((event.clientY - rect.top) / rect.height) * deviceScreenHeight);

            sendMultiTouch('pinch_zoom', centerX, centerY, null, null, scale);
        }

        // Touch handling for mobile devices
        let touchStartData = null;

        function handleTouchStart(event) {
            event.preventDefault();
            if (event.touches.length === 1) {
                const touch = event.touches[0];
                const rect = mirrorImage.getBoundingClientRect();
                touchStartData = {
                    x: touch.clientX - rect.left,
                    y: touch.clientY - rect.top,
                    time: Date.now()
                };
            }
        }

        function handleTouchMove(event) {
            event.preventDefault();
        }

        function handleTouchEnd(event) {
            event.preventDefault();
            if (touchStartData && event.changedTouches.length === 1) {
                const touch = event.changedTouches[0];
                const rect = mirrorImage.getBoundingClientRect();
                const endX = touch.clientX - rect.left;
                const endY = touch.clientY - rect.top;
                const timeDiff = Date.now() - touchStartData.time;

                // If it's a quick tap (less than 200ms and small movement)
                const distance = Math.sqrt(Math.pow(endX - touchStartData.x, 2) + Math.pow(endY - touchStartData.y, 2));
                if (timeDiff < 200 && distance < 10) {
                    const deviceX = Math.round((touchStartData.x / rect.width) * deviceScreenWidth);
                    const deviceY = Math.round((touchStartData.y / rect.height) * deviceScreenHeight);
                    sendTouchTap(deviceX, deviceY);
                }
            }
            touchStartData = null;
        }

        // Power button simulation
        async function powerButton(action) {
            const deviceId = deviceIdInput.value;
            if (!deviceId) { logOutput('Please select a Device ID.'); return; }

            logOutput(`Sending power button command (${action}) to ${deviceId}...`);
            try {
                const response = await fetch('/api/power_button', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deviceId, action })
                });
                const data = await response.json();
                logOutput(data.message);
            } catch (error) {
                logOutput(`Network error sending power button command: ${error.message}`);
            }
        }

        // Multi-touch gesture simulation
        async function sendMultiTouch(gesture, x1, y1, x2, y2, scale) {
            const deviceId = deviceIdInput.value;
            if (!deviceId) { logOutput('Please select a Device ID.'); return; }

            try {
                const response = await fetch('/api/multi_touch', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deviceId, gesture, x1, y1, x2, y2, scale })
                });
                const data = await response.json();
                logOutput(data.message);
            } catch (error) {
                logOutput(`Network error sending multi-touch gesture: ${error.message}`);
            }
        }

        // Pinch zoom gesture
        function pinchZoom(scale) {
            const centerX = deviceScreenWidth / 2;
            const centerY = deviceScreenHeight / 2;
            sendMultiTouch('pinch_zoom', centerX, centerY, null, null, scale);
        }

        // Two-finger scroll gesture
        function twoFingerScroll(direction) {
            const centerX = deviceScreenWidth / 2;
            const startY = deviceScreenHeight * 0.6;
            const endY = direction === 'up' ? deviceScreenHeight * 0.3 : deviceScreenHeight * 0.8;
            sendMultiTouch('two_finger_scroll', centerX, startY, centerX, endY);
        }

        // Show installed devices history
        async function showInstalledDevices() {
            logOutput('Loading device installation history...');
            try {
                const response = await fetch('/api/installed_devices');
                const data = await response.json();

                if (data.success) {
                    const historyDiv = document.getElementById('installationHistory');
                    const listDiv = document.getElementById('installedDevicesList');

                    if (data.devices.length > 0) {
                        listDiv.innerHTML = `
                            <table class="installation-table">
                                <thead>
                                    <tr>
                                        <th>Device ID</th>
                                        <th>Alias</th>
                                        <th>Status</th>
                                        <th>First Seen</th>
                                        <th>Last Seen</th>
                                        <th>Connections</th>
                                        <th>Uptime</th>
                                        <th>Currently Connected</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.devices.map(device => `
                                        <tr>
                                            <td>${device.deviceId}</td>
                                            <td>${device.aliases.join(', ') || 'None'}</td>
                                            <td><span class="status-badge status-${device.installationStatus.replace('_', '-')}">${device.installationStatus.replace('_', ' ').toUpperCase()}</span></td>
                                            <td>${new Date(device.firstSeen).toLocaleString()}</td>
                                            <td>${new Date(device.lastSeen).toLocaleString()}</td>
                                            <td>${device.totalConnections}</td>
                                            <td>${device.uptime}</td>
                                            <td>${device.isCurrentlyConnected ? '🟢 Yes' : '🔴 No'}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        `;
                        historyDiv.style.display = 'block';
                        logOutput(`Found ${data.devices.length} devices in installation history.`);
                    } else {
                        listDiv.innerHTML = '<p>No devices found in installation history.</p>';
                        historyDiv.style.display = 'block';
                    }
                } else {
                    logOutput(`Error loading installation history: ${data.message}`);
                }
            } catch (error) {
                logOutput(`Network error loading installation history: ${error.message}`);
            }
        }

        // Set device alias
        async function setDeviceAlias(deviceId) {
            const alias = prompt(`Enter a friendly name for device ${deviceId}:`);
            if (alias && alias.trim()) {
                try {
                    const response = await fetch(`/api/set_device_alias/${deviceId}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ alias: alias.trim() })
                    });
                    const data = await response.json();

                    if (data.success) {
                        logOutput(`Alias "${alias}" set for device ${deviceId}`);
                        // Refresh the device list to show the new alias
                        listDevices();
                    } else {
                        logOutput(`Error setting alias: ${data.message}`);
                    }
                } catch (error) {
                    logOutput(`Network error setting alias: ${error.message}`);
                }
            }
        }

        // Refresh device IP
        async function refreshDeviceIP(deviceId) {
            logOutput(`🔄 Refreshing IP for device ${deviceId}...`);
            try {
                const response = await fetch(`/api/discover_network/${deviceId}`);
                const data = await response.json();

                if (data.success) {
                    logOutput(`📍 Device ${deviceId} IP: ${data.networkInfo.ip || 'Unknown'}`);
                    if (data.networkInfo.mac) {
                        logOutput(`🔗 MAC Address: ${data.networkInfo.mac}`);
                    }
                    if (data.networkInfo.ssid) {
                        logOutput(`📶 WiFi Network: ${data.networkInfo.ssid}`);
                    }
                    // Refresh the device list to show updated info
                    listDevices();
                } else {
                    logOutput(`❌ Failed to refresh IP: ${data.message}`);
                }
            } catch (error) {
                logOutput(`Network error refreshing IP: ${error.message}`);
            }
        }

        // Refresh all device IPs
        async function refreshAllIPs() {
            logOutput('🔄 Refreshing IPs for all connected devices...');
            try {
                const response = await fetch('/api/refresh_all_ips', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                if (data.success) {
                    logOutput(`✅ ${data.message}`);
                    data.results.forEach(result => {
                        if (result.success) {
                            logOutput(`📍 ${result.deviceId}: ${result.currentIP}`);
                        } else {
                            logOutput(`❌ ${result.deviceId}: ${result.error}`);
                        }
                    });
                    // Refresh the device list
                    listDevices();
                } else {
                    logOutput(`❌ Failed to refresh IPs: ${data.message}`);
                }
            } catch (error) {
                logOutput(`Network error refreshing all IPs: ${error.message}`);
            }
        }

        // Test device connectivity
        async function testDeviceConnectivity(deviceId) {
            logOutput(`🧪 Testing connectivity for device ${deviceId}...`);
            try {
                const response = await fetch(`/api/test_connectivity/${deviceId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();

                if (data.success) {
                    const status = data.isOnline ? '🟢 Online' : '🔴 Offline';
                    logOutput(`${status} - Device ${deviceId}`);

                    data.tests.forEach(test => {
                        const icon = test.success ? '✅' : '❌';
                        const method = test.method.toUpperCase();
                        logOutput(`${icon} ${method}: ${test.success ? 'Success' : test.error}`);
                    });

                    // Refresh device list to update status
                    listDevices();
                } else {
                    logOutput(`❌ Connectivity test failed: ${data.message}`);
                }
            } catch (error) {
                logOutput(`Network error testing connectivity: ${error.message}`);
            }
        }

        // Test all device connectivity
        async function testAllConnectivity() {
            logOutput('🧪 Testing connectivity for all devices...');
            const deviceSelect = document.getElementById('deviceId');
            const devices = Array.from(deviceSelect.options)
                               .filter(option => option.value !== '')
                               .map(option => option.value);

            if (devices.length === 0) {
                logOutput('No devices found to test.');
                return;
            }

            for (const deviceId of devices) {
                await testDeviceConnectivity(deviceId);
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        // Show IP history for device
        async function showIPHistory(deviceId) {
            logOutput(`📊 Loading IP history for device ${deviceId}...`);
            try {
                const response = await fetch(`/api/device_history/${deviceId}`);
                const data = await response.json();

                if (data.success && data.device.ipHistory) {
                    const history = data.device.ipHistory;
                    if (history.length > 0) {
                        logOutput(`📊 IP History for ${deviceId}:`);
                        history.forEach((entry, index) => {
                            const time = new Date(entry.timestamp).toLocaleString();
                            logOutput(`${index + 1}. ${entry.ip} (${time})`);
                        });
                        if (data.device.currentIP) {
                            logOutput(`Current: ${data.device.currentIP}`);
                        }
                    } else {
                        logOutput(`No IP history found for device ${deviceId}`);
                    }
                } else {
                    logOutput(`❌ Failed to load IP history: ${data.message || 'Device not found'}`);
                }
            } catch (error) {
                logOutput(`Network error loading IP history: ${error.message}`);
            }
        }

        // Check device lock status
        async function checkLockStatus() {
            const deviceId = deviceIdInput.value;
            if (!deviceId) { logOutput('Please select a Device ID.'); return; }

            logOutput('Checking device lock status...');
            try {
                const response = await fetch(`/api/lock_status/${deviceId}`);
                const data = await response.json();

                if (data.success) {
                    const statusDiv = document.getElementById('lockStatus');
                    const isLocked = data.isLocked;
                    const wakefulness = data.wakefulness;

                    statusDiv.innerHTML = `
                        <strong>Device Status:</strong><br>
                        🔋 Power: ${wakefulness === 'awake' ? '✅ Awake' : '😴 Asleep'}<br>
                        🔒 Lock: ${isLocked ? '🔒 Locked' : '🔓 Unlocked'}<br>
                        ${isLocked ? '<span style="color: red;">⚠️ Device has secure lock screen</span>' : '<span style="color: green;">✅ Device is unlocked</span>'}
                    `;
                    statusDiv.style.display = 'block';

                    logOutput(`Device is ${wakefulness} and ${isLocked ? 'locked' : 'unlocked'}`);

                    if (isLocked) {
                        logOutput('⚠️ Device has a secure lock screen. Try the advanced unlock methods below.');
                    }
                } else {
                    logOutput(`Error checking lock status: ${data.message}`);
                }
            } catch (error) {
                logOutput(`Network error checking lock status: ${error.message}`);
            }
        }

        // Advanced unlock methods
        async function advancedUnlock(method) {
            const deviceId = deviceIdInput.value;
            if (!deviceId) { logOutput('Please select a Device ID.'); return; }

            const methodNames = {
                'wake_sequence': 'Wake & Unlock Sequence',
                'wake_and_swipe': 'Wake Device',
                'swipe_up_small': 'Swipe Up (Small Screen)',
                'menu_key': 'Menu Key Unlock',
                'multiple_swipes': 'Multiple Swipe Attempts'
            };

            logOutput(`Trying ${methodNames[method]} on ${deviceId}...`);
            try {
                const response = await fetch('/api/advanced_unlock', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deviceId, method })
                });
                const data = await response.json();
                logOutput(data.message);

                // Check status after unlock attempt
                setTimeout(() => {
                    checkLockStatus();
                }, 2000);

            } catch (error) {
                logOutput(`Network error with advanced unlock: ${error.message}`);
            }
        }

    </script>
</body>
</html>
