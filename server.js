const express = require('express');
const { exec, spawn } = require('child_process');
const path = require('path');
const http = require('http'); // Import http module
const WebSocket = require('ws'); // Import ws module
const multer = require('multer'); // Import multer for file uploads
const fs = require('fs'); // Import fs module for file system operations

// Configure multer to store uploads with proper file extensions
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/');
    },
    filename: function (req, file, cb) {
        // Generate a unique filename while preserving the original extension
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        cb(null, file.fieldname + '-' + uniqueSuffix + ext);
    }
});

const upload = multer({ storage: storage });

// Device installation and tracking system
let deviceRegistry = {};
const DEVICE_REGISTRY_FILE = 'device_registry.json';

// Load existing device registry
function loadDeviceRegistry() {
    try {
        if (fs.existsSync(DEVICE_REGISTRY_FILE)) {
            const data = fs.readFileSync(DEVICE_REGISTRY_FILE, 'utf8');
            deviceRegistry = JSON.parse(data);
        }
    } catch (error) {
        console.error('Error loading device registry:', error);
        deviceRegistry = {};
    }
}

// Save device registry
function saveDeviceRegistry() {
    try {
        fs.writeFileSync(DEVICE_REGISTRY_FILE, JSON.stringify(deviceRegistry, null, 2));
    } catch (error) {
        console.error('Error saving device registry:', error);
    }
}

// Track device activity with dynamic IP management
function trackDeviceActivity(deviceId, event, additionalData = {}) {
    const now = new Date().toISOString();

    if (!deviceRegistry[deviceId]) {
        deviceRegistry[deviceId] = {
            firstSeen: now,
            lastSeen: now,
            installationStatus: 'newly_discovered',
            connectionHistory: [],
            deviceInfo: {},
            totalConnections: 0,
            totalUptime: 0,
            aliases: [],
            // Dynamic IP management fields
            currentIP: null,
            ipHistory: [],
            macAddress: null,
            lastIPChange: null,
            connectionMethods: [],
            preferredConnection: 'usb',
            networkInfo: {
                ssid: null,
                gateway: null,
                subnet: null
            },
            connectivity: {
                isOnline: false,
                lastPing: null,
                responseTime: null
            }
        };
    }

    deviceRegistry[deviceId].lastSeen = now;
    deviceRegistry[deviceId].connectionHistory.push({
        timestamp: now,
        event: event,
        ...additionalData
    });

    // Handle IP tracking
    if (additionalData.ip && additionalData.ip !== deviceRegistry[deviceId].currentIP) {
        updateDeviceIP(deviceId, additionalData.ip);
    }

    // Handle MAC address discovery
    if (additionalData.macAddress && !deviceRegistry[deviceId].macAddress) {
        deviceRegistry[deviceId].macAddress = additionalData.macAddress;
    }

    if (event === 'adb_connected') {
        deviceRegistry[deviceId].totalConnections++;
        deviceRegistry[deviceId].installationStatus = 'installed';

        // Ensure connectivity object exists before setting properties
        if (!deviceRegistry[deviceId].connectivity) {
            deviceRegistry[deviceId].connectivity = {
                isOnline: false,
                lastPing: null,
                responseTime: null
            };
        }
        deviceRegistry[deviceId].connectivity.isOnline = true;
    }

    saveDeviceRegistry();
}

// Update device IP address with history tracking
function updateDeviceIP(deviceId, newIP) {
    const device = deviceRegistry[deviceId];
    if (!device) return;

    // Add old IP to history if it exists and is different
    if (device.currentIP && device.currentIP !== newIP) {
        device.ipHistory.push({
            ip: device.currentIP,
            timestamp: new Date().toISOString(),
            event: 'ip_changed'
        });

        // Keep only last 10 IP addresses
        if (device.ipHistory.length > 10) {
            device.ipHistory = device.ipHistory.slice(-10);
        }

        device.lastIPChange = new Date().toISOString();
        console.log(`📍 Device ${deviceId} IP changed: ${device.currentIP} → ${newIP}`);
    }

    device.currentIP = newIP;
    saveDeviceRegistry();
}

// Initialize device registry on startup
loadDeviceRegistry();

const app = express();
app.use(express.json()); // Enable JSON body parsing
const port = 3000;

const server = http.createServer(app); // Create HTTP server
const wssScreen = new WebSocket.Server({ server }); // Attach WebSocket server to HTTP server

// Add error handling for the server
server.on('error', (error) => {
    console.error('Server error:', error);
});

// Add error handling for WebSocket server
wssScreen.on('error', (error) => {
    console.error('WebSocket server error:', error);
});

// Define the path to adb.exe
const adbPath = `"${path.join(__dirname, 'platform-tools', 'adb.exe')}"`;
console.log(`ADB Path: ${adbPath}`);

app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// API endpoint to list ADB devices with installation tracking
app.get('/api/list_devices', (req, res) => {
    exec(`${adbPath} devices`, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to execute ADB command: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }

        const lines = stdout.split('\n');
        const devices = [];
        lines.forEach(line => {
            const trimmedLine = line.trim();
            if (trimmedLine && !trimmedLine.startsWith('List of devices attached')) {
                const parts = trimmedLine.trim().split(/\s+/);
                if (parts.length >= 2) {
                    const deviceId = parts[0];
                    const status = parts[1];

                    // Get device network info
                    getDeviceNetworkInfo(deviceId).then(networkInfo => {
                        // Track device connection with network data
                        trackDeviceActivity(deviceId, 'adb_connected', {
                            status: status,
                            ip: networkInfo.ip,
                            macAddress: networkInfo.mac
                        });
                    }).catch(err => {
                        // Track without network info if it fails
                        trackDeviceActivity(deviceId, 'adb_connected', { status: status });
                    });

                    // Get installation info
                    const installInfo = deviceRegistry[deviceId] || {};

                    devices.push({
                        id: deviceId,
                        status: status,
                        displayName: `${deviceId} (${status})`,
                        installationStatus: installInfo.installationStatus || 'newly_discovered',
                        firstSeen: installInfo.firstSeen || new Date().toISOString(),
                        lastSeen: installInfo.lastSeen || new Date().toISOString(),
                        totalConnections: installInfo.totalConnections || 1,
                        aliases: installInfo.aliases || [],
                        // Dynamic IP info
                        currentIP: installInfo.currentIP || 'Unknown',
                        ipHistory: installInfo.ipHistory || [],
                        macAddress: installInfo.macAddress || 'Unknown',
                        connectivity: installInfo.connectivity || { isOnline: false }
                    });
                }
            }
        });
        res.json({ success: true, devices: devices });
    });
});

// API endpoint to get device installation history
app.get('/api/device_history/:deviceId', (req, res) => {
    const { deviceId } = req.params;
    const device = deviceRegistry[deviceId];

    if (device) {
        res.json({
            success: true,
            device: {
                deviceId: deviceId,
                ...device,
                uptime: calculateUptime(device.connectionHistory)
            }
        });
    } else {
        res.json({
            success: false,
            message: 'Device not found in registry'
        });
    }
});

// API endpoint to get all installed devices
app.get('/api/installed_devices', (req, res) => {
    const installedDevices = Object.keys(deviceRegistry).map(deviceId => {
        const device = deviceRegistry[deviceId];
        return {
            deviceId: deviceId,
            installationStatus: device.installationStatus,
            firstSeen: device.firstSeen,
            lastSeen: device.lastSeen,
            totalConnections: device.totalConnections,
            aliases: device.aliases,
            uptime: calculateUptime(device.connectionHistory),
            isCurrentlyConnected: isDeviceCurrentlyConnected(deviceId)
        };
    });

    res.json({ success: true, devices: installedDevices });
});

// API endpoint to set device alias
app.post('/api/set_device_alias/:deviceId', (req, res) => {
    const { deviceId } = req.params;
    const { alias } = req.body;

    if (!deviceRegistry[deviceId]) {
        trackDeviceActivity(deviceId, 'alias_set', { alias: alias });
    } else {
        deviceRegistry[deviceId].aliases = deviceRegistry[deviceId].aliases || [];
        if (!deviceRegistry[deviceId].aliases.includes(alias)) {
            deviceRegistry[deviceId].aliases.push(alias);
        }
        trackDeviceActivity(deviceId, 'alias_updated', { alias: alias });
    }

    saveDeviceRegistry();
    res.json({ success: true, message: `Alias "${alias}" set for device ${deviceId}` });
});

// Helper function to calculate uptime
function calculateUptime(connectionHistory) {
    if (!connectionHistory || connectionHistory.length === 0) return '0m';

    const firstConnection = new Date(connectionHistory[0].timestamp);
    const lastActivity = new Date(connectionHistory[connectionHistory.length - 1].timestamp);
    const diffMs = lastActivity - firstConnection;

    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
        return `${hours}h ${minutes}m`;
    } else {
        return `${minutes}m`;
    }
}

// Helper function to check if device is currently connected
function isDeviceCurrentlyConnected(deviceId) {
    // This would need to be implemented with actual ADB check
    // For now, return true if last seen was within 5 minutes
    const device = deviceRegistry[deviceId];
    if (!device) return false;

    const lastSeen = new Date(device.lastSeen);
    const now = new Date();
    const diffMinutes = (now - lastSeen) / (1000 * 60);

    return diffMinutes < 5;
}

// Get device network information
async function getDeviceNetworkInfo(deviceId) {
    return new Promise((resolve, reject) => {
        // Get IP address
        exec(`${adbPath} -s ${deviceId} shell ip route | grep wlan0`, (error, stdout, stderr) => {
            if (error) {
                reject(error);
                return;
            }

            const ipMatch = stdout.match(/src (\d+\.\d+\.\d+\.\d+)/);
            const ip = ipMatch ? ipMatch[1] : null;

            // Get MAC address
            exec(`${adbPath} -s ${deviceId} shell cat /sys/class/net/wlan0/address`, (macError, macStdout, macStderr) => {
                const mac = macError ? null : macStdout.trim();

                // Get network details
                exec(`${adbPath} -s ${deviceId} shell dumpsys wifi | grep "mWifiInfo"`, (wifiError, wifiStdout, wifiStderr) => {
                    let ssid = null;
                    if (!wifiError && wifiStdout) {
                        const ssidMatch = wifiStdout.match(/SSID: ([^,]+)/);
                        ssid = ssidMatch ? ssidMatch[1].replace(/"/g, '') : null;
                    }

                    resolve({
                        ip: ip,
                        mac: mac,
                        ssid: ssid,
                        timestamp: new Date().toISOString()
                    });
                });
            });
        });
    });
}

// API endpoint for dynamic IP discovery
app.get('/api/discover_network/:deviceId', async (req, res) => {
    const { deviceId } = req.params;

    try {
        const networkInfo = await getDeviceNetworkInfo(deviceId);

        // Update device registry with new network info
        if (deviceRegistry[deviceId]) {
            updateDeviceIP(deviceId, networkInfo.ip);
            deviceRegistry[deviceId].macAddress = networkInfo.mac;
            deviceRegistry[deviceId].networkInfo.ssid = networkInfo.ssid;
            saveDeviceRegistry();
        }

        res.json({
            success: true,
            deviceId: deviceId,
            networkInfo: networkInfo
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: `Failed to discover network info: ${error.message}`
        });
    }
});

// API endpoint to refresh all device IPs
app.post('/api/refresh_all_ips', async (req, res) => {
    const results = [];
    const connectedDevices = Object.keys(deviceRegistry).filter(deviceId =>
        isDeviceCurrentlyConnected(deviceId)
    );

    for (const deviceId of connectedDevices) {
        try {
            const networkInfo = await getDeviceNetworkInfo(deviceId);
            updateDeviceIP(deviceId, networkInfo.ip);

            results.push({
                deviceId: deviceId,
                success: true,
                currentIP: networkInfo.ip,
                mac: networkInfo.mac
            });
        } catch (error) {
            results.push({
                deviceId: deviceId,
                success: false,
                error: error.message
            });
        }
    }

    res.json({
        success: true,
        message: `Refreshed IPs for ${results.filter(r => r.success).length}/${results.length} devices`,
        results: results
    });
});

// API endpoint to test device connectivity
app.post('/api/test_connectivity/:deviceId', async (req, res) => {
    const { deviceId } = req.params;
    const device = deviceRegistry[deviceId];

    if (!device) {
        return res.status(404).json({ success: false, message: 'Device not found' });
    }

    const connectivityTests = [];

    // Test 1: ADB connection
    try {
        await new Promise((resolve, reject) => {
            exec(`${adbPath} -s ${deviceId} shell echo "test"`, (error, stdout, stderr) => {
                if (error) reject(error);
                else resolve(stdout);
            });
        });
        connectivityTests.push({ method: 'adb', success: true });
    } catch (error) {
        connectivityTests.push({ method: 'adb', success: false, error: error.message });
    }

    // Test 2: Ping current IP (if available)
    if (device.currentIP) {
        try {
            await new Promise((resolve, reject) => {
                exec(`ping -n 1 ${device.currentIP}`, (error, stdout, stderr) => {
                    if (error) reject(error);
                    else resolve(stdout);
                });
            });
            connectivityTests.push({ method: 'ping', success: true, ip: device.currentIP });
        } catch (error) {
            connectivityTests.push({ method: 'ping', success: false, ip: device.currentIP, error: error.message });
        }
    }

    // Update connectivity status
    const isOnline = connectivityTests.some(test => test.success);
    device.connectivity.isOnline = isOnline;
    device.connectivity.lastPing = new Date().toISOString();
    saveDeviceRegistry();

    res.json({
        success: true,
        deviceId: deviceId,
        isOnline: isOnline,
        tests: connectivityTests
    });
});

// API endpoint to reboot a device
app.post('/api/reboot_device', (req, res) => {
    const { deviceId } = req.body;
    if (!deviceId) {
        return res.status(400).json({ success: false, message: 'Device ID is required.' });
    }

    exec(`${adbPath} -s ${deviceId} reboot`, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to reboot device: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }
        res.json({ success: true, message: `Reboot command sent to ${deviceId}` });
    });
});

// API endpoint to send a key event
app.post('/api/send_keyevent', (req, res) => {
    const { deviceId, keyEvent } = req.body;
    if (!deviceId || !keyEvent) {
        return res.status(400).json({ success: false, message: 'Device ID and key event are required.' });
    }

    let adbKeyEventCode;
    switch (keyEvent.toUpperCase()) {
        case 'HOME':
            adbKeyEventCode = '3';
            break;
        case 'BACK':
            adbKeyEventCode = '4';
            break;
        case 'RECENTS':
            adbKeyEventCode = '187'; // APP_SWITCH
            break;
        default:
            return res.status(400).json({ success: false, message: 'Invalid key event. Supported: HOME, BACK, RECENTS.' });
    }

    exec(`${adbPath} -s ${deviceId} shell input keyevent ${adbKeyEventCode}`, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to send key event: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }
        res.json({ success: true, message: `Key event ${keyEvent} sent to ${deviceId}` });
    });
});



// API endpoint to send text input
app.post('/api/send_text', (req, res) => {
    const { deviceId, text } = req.body;
    if (!deviceId || !text) {
        return res.status(400).json({ success: false, message: 'Device ID and text are required.' });
    }

    // Escape single quotes in the text to prevent shell injection issues
    const escapedText = text.replace(/\'/g, "'\\''");

    exec(`${adbPath} -s ${deviceId} shell input text '${escapedText}'`, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to send text: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }
        res.json({ success: true, message: `Text sent to ${deviceId}` });
    });
});

// API endpoint to simulate a swipe up gesture
app.post('/api/swipe_up', (req, res) => {
    const { deviceId } = req.body;
    if (!deviceId) {
        return res.status(400).json({ success: false, message: 'Device ID is required.' });
    }

    // Get device resolution first to calculate proper coordinates
    exec(`${adbPath} -s ${deviceId} shell wm size`, (error, stdout, stderr) => {
        let startX, startY, endX, endY;

        if (!error && stdout) {
            const match = stdout.match(/Physical size: (\d+)x(\d+)/);
            if (match) {
                const width = parseInt(match[1]);
                const height = parseInt(match[2]);

                // Calculate coordinates based on actual screen size
                startX = Math.round(width / 2);     // Middle horizontally
                startY = Math.round(height * 0.8);  // 80% down from top
                endX = startX;                      // Same horizontal position
                endY = Math.round(height * 0.3);    // 30% down from top
            }
        }

        // Fallback coordinates if resolution detection fails
        if (!startX) {
            startX = 240; // Middle of 480px width
            startY = 640; // 80% of 800px height
            endX = 240;   // Same horizontal position
            endY = 240;   // 30% of 800px height
        }

        const duration = 500; // Slower swipe for better recognition

        exec(`${adbPath} -s ${deviceId} shell input swipe ${startX} ${startY} ${endX} ${endY} ${duration}`, (error, stdout, stderr) => {
            if (error) {
                console.error(`exec error: ${error}`);
                return res.status(500).json({ success: false, message: `Failed to send swipe up command: ${error.message}` });
            }
            if (stderr) {
                console.error(`adb stderr: ${stderr}`);
            }
            res.json({ success: true, message: `Swipe up command sent to ${deviceId} (${startX},${startY} → ${endX},${endY})` });
        });
    });
});

// API endpoint to unlock device
app.post('/api/unlock_device', (req, res) => {
    const { deviceId } = req.body;
    if (!deviceId) {
        return res.status(400).json({ success: false, message: 'Device ID is required.' });
    }

    exec(`${adbPath} -s ${deviceId} shell input keyevent 82`, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to unlock device: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }
        res.json({ success: true, message: `Unlock command sent to ${deviceId}` });
    });
});

// API endpoint to simulate a swipe left gesture
app.post('/api/swipe_left', (req, res) => {
    const { deviceId } = req.body;
    if (!deviceId) {
        return res.status(400).json({ success: false, message: 'Device ID is required.' });
    }

    // Coordinates for a swipe left (start X, start Y, end X, end Y, duration in ms)
    const startX = 900; // Near the right edge
    const startY = 500; // Middle of the screen vertically
    const endX = 100;   // Near the left edge
    const endY = 500;    // Middle of the screen vertically
    const duration = 200; // Fast swipe

    exec(`${adbPath} -s ${deviceId} shell input swipe ${startX} ${startY} ${endX} ${endY} ${duration}`, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to send swipe left command: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }
        res.json({ success: true, message: `Swipe left command sent to ${deviceId}` });
    });
});

// API endpoint to simulate a swipe right gesture
app.post('/api/swipe_right', (req, res) => {
    const { deviceId } = req.body;
    if (!deviceId) {
        return res.status(400).json({ success: false, message: 'Device ID is required.' });
    }

    // Coordinates for a swipe right (start X, start Y, end X, end Y, duration in ms)
    const startX = 100; // Near the left edge
    const startY = 500; // Middle of the screen vertically
    const endX = 900;   // Near the right edge
    const endY = 500;    // Middle of the screen vertically
    const duration = 200; // Fast swipe

    exec(`${adbPath} -s ${deviceId} shell input swipe ${startX} ${startY} ${endX} ${endY} ${duration}`, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to send swipe right command: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }
        res.json({ success: true, message: `Swipe right command sent to ${deviceId}` });
    });
});

// API endpoint to simulate touch/tap at specific coordinates
app.post('/api/touch_tap', (req, res) => {
    const { deviceId, x, y } = req.body;
    if (!deviceId || x === undefined || y === undefined) {
        return res.status(400).json({ success: false, message: 'Device ID, x, and y coordinates are required.' });
    }

    // Validate coordinates are numbers
    const tapX = parseInt(x);
    const tapY = parseInt(y);
    if (isNaN(tapX) || isNaN(tapY)) {
        return res.status(400).json({ success: false, message: 'Coordinates must be valid numbers.' });
    }

    exec(`${adbPath} -s ${deviceId} shell input tap ${tapX} ${tapY}`, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to send tap command: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }
        res.json({ success: true, message: `Tap sent to ${deviceId} at coordinates (${tapX}, ${tapY})` });
    });
});

// API endpoint to simulate multi-touch gestures
app.post('/api/multi_touch', (req, res) => {
    const { deviceId, gesture, x1, y1, x2, y2, scale } = req.body;
    if (!deviceId || !gesture) {
        return res.status(400).json({ success: false, message: 'Device ID and gesture type are required.' });
    }

    let command;
    if (gesture === 'pinch_zoom') {
        // Pinch to zoom gesture - two fingers moving apart or together
        const centerX = parseInt(x1) || 500;
        const centerY = parseInt(y1) || 500;
        const scaleValue = parseFloat(scale) || 1.5;

        // Calculate start and end positions for two fingers
        const distance = 100 * scaleValue;
        const startX1 = centerX - 50;
        const startY1 = centerY;
        const endX1 = centerX - distance/2;
        const endY1 = centerY;
        const startX2 = centerX + 50;
        const startY2 = centerY;
        const endX2 = centerX + distance/2;
        const endY2 = centerY;

        // Use swipe command to simulate pinch (ADB doesn't have native multi-touch)
        command = `${adbPath} -s ${deviceId} shell input swipe ${startX1} ${startY1} ${endX1} ${endY1} 500 & ${adbPath} -s ${deviceId} shell input swipe ${startX2} ${startY2} ${endX2} ${endY2} 500`;
    } else if (gesture === 'two_finger_scroll') {
        // Two finger scroll gesture
        const startX1 = parseInt(x1) || 400;
        const startY1 = parseInt(y1) || 600;
        const endX1 = parseInt(x2) || 400;
        const endY1 = parseInt(y2) || 300;
        const startX2 = startX1 + 100;
        const startY2 = startY1;
        const endX2 = endX1 + 100;
        const endY2 = endY1;

        command = `${adbPath} -s ${deviceId} shell input swipe ${startX1} ${startY1} ${endX1} ${endY1} 300 & ${adbPath} -s ${deviceId} shell input swipe ${startX2} ${startY2} ${endX2} ${endY2} 300`;
    } else {
        return res.status(400).json({ success: false, message: 'Invalid gesture type. Supported: pinch_zoom, two_finger_scroll.' });
    }

    exec(command, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to send multi-touch gesture: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }
        res.json({ success: true, message: `${gesture} gesture sent to ${deviceId}` });
    });
});

// API endpoint to simulate power button press (sleep/wake)
app.post('/api/power_button', (req, res) => {
    const { deviceId, action } = req.body;
    if (!deviceId) {
        return res.status(400).json({ success: false, message: 'Device ID is required.' });
    }

    // Power button keyevent code is 26
    const keyEventCode = '26';
    const actionText = action === 'wake' ? 'wake' : 'sleep/wake';

    exec(`${adbPath} -s ${deviceId} shell input keyevent ${keyEventCode}`, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to send power button command: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }
        res.json({ success: true, message: `Power button (${actionText}) command sent to ${deviceId}` });
    });
});

// API endpoint to get device screen resolution
app.get('/api/screen_resolution/:deviceId', (req, res) => {
    const { deviceId } = req.params;
    if (!deviceId) {
        return res.status(400).json({ success: false, message: 'Device ID is required.' });
    }

    exec(`${adbPath} -s ${deviceId} shell wm size`, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to get screen resolution: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }

        // Parse the output to extract resolution
        const match = stdout.match(/Physical size: (\d+)x(\d+)/);
        if (match) {
            const width = parseInt(match[1]);
            const height = parseInt(match[2]);
            res.json({ success: true, width, height, resolution: `${width}x${height}` });
        } else {
            res.json({ success: false, message: 'Could not parse screen resolution' });
        }
    });
});

// API endpoint to check device lock screen status
app.get('/api/lock_status/:deviceId', (req, res) => {
    const { deviceId } = req.params;
    if (!deviceId) {
        return res.status(400).json({ success: false, message: 'Device ID is required.' });
    }

    // Check multiple lock screen indicators
    const commands = [
        `${adbPath} -s ${deviceId} shell dumpsys power | grep "mWakefulness"`,
        `${adbPath} -s ${deviceId} shell dumpsys window policy | grep "isStatusBarKeyguard"`,
        `${adbPath} -s ${deviceId} shell dumpsys trust`
    ];

    let results = {};
    let completed = 0;

    commands.forEach((command, index) => {
        exec(command, (error, stdout, stderr) => {
            completed++;

            if (index === 0) { // Power state
                results.wakefulness = stdout.includes('Awake') ? 'awake' : 'asleep';
            } else if (index === 1) { // Keyguard state
                results.keyguard = stdout.includes('true') ? 'locked' : 'unlocked';
            } else if (index === 2) { // Trust state
                results.trust = stdout;
            }

            if (completed === commands.length) {
                res.json({
                    success: true,
                    wakefulness: results.wakefulness,
                    keyguard: results.keyguard,
                    isLocked: results.keyguard === 'locked',
                    trustInfo: results.trust
                });
            }
        });
    });
});

// API endpoint for advanced unlock attempts
app.post('/api/advanced_unlock', (req, res) => {
    const { deviceId, method } = req.body;
    if (!deviceId) {
        return res.status(400).json({ success: false, message: 'Device ID is required.' });
    }

    let command;
    let description;

    switch (method) {
        case 'wake_and_swipe':
            // Wake device and swipe up (Windows compatible)
            command = `${adbPath} -s ${deviceId} shell input keyevent 26`;
            description = 'Wake device';
            break;
        case 'swipe_up_small':
            // Swipe up for small screen (480x800)
            command = `${adbPath} -s ${deviceId} shell input swipe 240 600 240 200 500`;
            description = 'Swipe up (small screen)';
            break;
        case 'menu_key':
            // Try menu key (sometimes works on older devices)
            command = `${adbPath} -s ${deviceId} shell input keyevent 82`;
            description = 'Menu key unlock attempt';
            break;
        case 'dismiss_keyguard':
            // Try to dismiss keyguard (requires special permissions)
            command = `${adbPath} -s ${deviceId} shell am start -n com.android.settings/.Settings`;
            description = 'Attempt to open settings (may bypass some locks)';
            break;
        case 'multiple_swipes':
            // Multiple swipe attempts for small screen
            command = `${adbPath} -s ${deviceId} shell "input swipe 240 600 240 200 300; input swipe 240 600 240 200 300; input swipe 240 600 240 200 300"`;
            description = 'Multiple swipe attempts';
            break;
        case 'wake_sequence':
            // Complete wake and unlock sequence
            command = `${adbPath} -s ${deviceId} shell "input keyevent 26; input swipe 240 600 240 200 500"`;
            description = 'Wake and swipe sequence';
            break;
        default:
            return res.status(400).json({ success: false, message: 'Invalid unlock method' });
    }

    exec(command, (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to execute unlock method: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }
        res.json({ success: true, message: `${description} executed on ${deviceId}` });
    });
});

// WebSocket server for screen mirroring
wssScreen.on('connection', (ws, req) => {
    const deviceId = new URL(req.url, `http://${req.headers.host}`).searchParams.get('deviceId');

    if (!deviceId) {
        ws.send('Error: Device ID is required.');
        ws.close();
        return;
    }

    let captureInterval;

    const startCapture = () => {
        exec(`${adbPath} -s ${deviceId} exec-out screencap -p`, { encoding: 'buffer', maxBuffer: 50 * 1024 * 1024 }, (error, stdout, stderr) => {
            if (error) {
                console.error(`Screen capture exec error: ${error}`);
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(`Error: ${error.message}`);
                }
                return;
            }
            if (stderr && stderr.length > 0) {
                console.error(`Screen capture stderr: ${stderr.toString()}`);
            }

            if (ws.readyState === WebSocket.OPEN) {
                ws.send(stdout); // Send raw image buffer
            }
        });
    };

    // Start capturing screenshots every 500ms (2 frames per second) to reduce load
    captureInterval = setInterval(startCapture, 500);

    ws.on('close', () => {
        console.log('Screen mirroring WebSocket closed. Clearing interval.');
        clearInterval(captureInterval); // Stop capturing when WebSocket closes
    });

    ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        clearInterval(captureInterval); // Clear interval on error
    });
});

// API endpoint to install an APK
app.post('/api/install_apk', upload.single('apkFile'), (req, res) => {
    const { deviceId } = req.body;
    const apkFile = req.file;

    if (!deviceId) {
        return res.status(400).json({ success: false, message: 'Device ID is required.' });
    }

    if (!apkFile) {
        return res.status(400).json({ success: false, message: 'No APK file uploaded.' });
    }

    const apkPath = `"${apkFile.path}"`; // Quote the path to handle spaces

    exec(`${adbPath} -s ${deviceId} install ${apkPath}`, (error, stdout, stderr) => {
        // Clean up the uploaded file after installation attempt
        fs.unlink(apkFile.path, (err) => {
            if (err) console.error(`Error deleting temporary APK file: ${err}`);
        });

        if (error) {
            console.error(`exec error: ${error}`);
            return res.status(500).json({ success: false, message: `Failed to install APK: ${error.message}` });
        }
        if (stderr) {
            console.error(`adb stderr: ${stderr}`);
        }
        res.json({ success: true, message: `APK ${apkFile.originalname} installed on ${deviceId}` });
    });
});

// API endpoint to install an APK on multiple devices (bulk install)
app.post('/api/install_apk_bulk', upload.single('apkFile'), (req, res) => {
    const deviceIds = Array.isArray(req.body.deviceIds) ? req.body.deviceIds : [req.body.deviceIds];
    const apkFile = req.file;

    if (!deviceIds || deviceIds.length === 0) {
        return res.status(400).json({ success: false, message: 'At least one device ID is required.' });
    }

    if (!apkFile) {
        return res.status(400).json({ success: false, message: 'No APK file uploaded.' });
    }

    const apkPath = `"${apkFile.path}"`; // Quote the path to handle spaces
    const results = [];
    let completedInstalls = 0;

    // Function to clean up file after all installations
    const cleanupFile = () => {
        fs.unlink(apkFile.path, (err) => {
            if (err) console.error(`Error deleting temporary APK file: ${err}`);
        });
    };

    // Install on each device
    deviceIds.forEach((deviceId, index) => {
        exec(`${adbPath} -s ${deviceId} install ${apkPath}`, (error, stdout, stderr) => {
            completedInstalls++;

            if (error) {
                console.error(`exec error for ${deviceId}: ${error}`);
                results.push({
                    deviceId,
                    success: false,
                    message: `Failed to install APK on ${deviceId}: ${error.message}`
                });
            } else {
                if (stderr) {
                    console.error(`adb stderr for ${deviceId}: ${stderr}`);
                }
                results.push({
                    deviceId,
                    success: true,
                    message: `APK ${apkFile.originalname} installed successfully on ${deviceId}`
                });
            }

            // If this is the last installation, send response and cleanup
            if (completedInstalls === deviceIds.length) {
                cleanupFile();
                const successCount = results.filter(r => r.success).length;
                const failCount = results.filter(r => !r.success).length;

                res.json({
                    success: true,
                    message: `Bulk installation completed: ${successCount} successful, ${failCount} failed`,
                    results: results
                });
            }
        });
    });
});

server.listen(port, () => {
  console.log(`Server listening at http://localhost:${port}`);
});